# 奇安信流量传感器API工具

一个用于奇安信流量传感器的Python API工具，支持自动登录、告警查询和详情获取。

## 功能特性

- ✅ **自动登录**: 支持验证码自动识别和登录重试机制
- ✅ **告警查询**: 支持多种条件的告警列表查询
- ✅ **详情获取**: 根据告警ID获取详细的数据包信息
- ✅ **数据导出**: 支持将查询结果导出为JSON格式
- ✅ **字段解析**: 自动解析和格式化危险级别、攻击结果等关键字段

## 文件结构

```
├── login.py           # 登录模块 - 处理登录逻辑和基础API调用
├── query_list.py      # 查询模块 - 告警列表查询和过滤
├── alert_detail.py    # 详情模块 - 根据ID查询告警详情
├── example.py         # 使用示例 - 完整的使用演示
└── README.md          # 说明文档
```

## 依赖安装

```bash
pip install requests ddddocr
```

## 快速开始

### 基本使用

```python
from query_list import create_query_manager
from alert_detail import AlertDetailManager

# 1. 创建查询管理器（自动登录）
query_manager = create_query_manager(
    base_url="https://*************",
    username="admin",
    password="",
    max_retries=5,
    verbose=True
)

# 2. 查询告警列表
alerts = query_manager.get_alert_list(limit=10)
print(f"获取到 {len(alerts['items'])} 条告警")

# 3. 查询告警详情
detail_manager = AlertDetailManager(query_manager.simulator)
for item in alerts['items']:
    alert_id = item.get('id')
    if alert_id:
        detail_info = detail_manager.get_alert_basic_info(alert_id)
        print(f"告警 {alert_id}: {detail_info['rule_name']}")
```

### 运行示例

```bash
python example.py
```

## 核心模块说明

### 1. login.py - 登录模块

**主要类**: `QianXinLoginSimulator`

**核心功能**:
- 模拟完整的登录流程
- 自动验证码识别（使用ddddocr）
- 登录重试机制（默认最多5次）
- 维护登录会话和CSRF令牌

**主要方法**:
```python
simulator = QianXinLoginSimulator("https://*************")
success = simulator.login("admin", "", auto_recognize=True, max_retries=5)
```

### 2. query_list.py - 查询模块

**主要类**: `AlertQueryManager`

**核心功能**:
- 告警列表查询
- 多种过滤条件支持
- 预定义查询方法（高危告警、Web攻击等）
- 统计分析功能

**主要方法**:
```python
# 基本查询
alerts = query_manager.get_alert_list(limit=10)

# 高危告警
high_risk = query_manager.get_high_risk_alerts(limit=5)

# Web攻击
web_attacks = query_manager.get_web_attacks(limit=5)

# 时间范围查询
recent = query_manager.get_alerts_by_time_range(hours_ago=1)

# 统计信息
stats = query_manager.get_alert_statistics(hours=24)
```

### 3. alert_detail.py - 详情模块

**主要类**: `AlertDetailManager`

**核心功能**:
- 根据告警ID查询详细信息
- 解析数据包信息
- 提取网络请求响应详情
- 格式化显示和数据导出

**主要方法**:
```python
# 获取基本信息
basic_info = detail_manager.get_alert_basic_info(alert_id)

# 获取网络信息
network_info = detail_manager.get_alert_network_info(alert_id)

# 获取地理位置信息
location_info = detail_manager.get_alert_location_info(alert_id)

# 格式化显示
formatted = detail_manager.format_alert_detail(alert_id)

# 导出详情
filename = detail_manager.export_alert_detail_to_json(alert_id)
```

## 字段说明

### 危险级别 (hazard_level)
- 🚨 **危急** - 最高级别
- 🔴 **高危** - 高级别  
- 🟡 **中危** - 中级别
- 🟢 **低危** - 低级别

### 攻击结果 (host_state)
- ❌ **成功** - 攻击成功
- ⚠️ **企图** - 攻击企图
- ✅ **失败** - 攻击失败

## 配置说明

### 基本配置
```python
BASE_URL = "https://*************"  # 服务器地址
USERNAME = "admin"                   # 用户名
PASSWORD = ""                        # 密码
MAX_RETRIES = 5                      # 最大重试次数
```

### 查询参数
```python
# 时间范围（毫秒时间戳）
start_time = int(time.time() * 1000) - (24 * 60 * 60 * 1000)  # 24小时前
end_time = int(time.time() * 1000)  # 当前时间

# 分页参数
offset = 1    # 偏移量
limit = 10    # 每页数量

# 过滤条件
filters = {
    'hazard_level': '高危',
    'host_state': '成功',
    'is_web_attack': '1',
    'status': '未处置'
}
```

## 使用场景

### 1. 安全监控
```python
# 查询高危且攻击成功的告警
critical_alerts = query_manager.search_alerts(
    hazard_level="危急",
    host_state="成功",
    limit=20
)
```

### 2. 威胁分析
```python
# 分析特定IP的攻击行为
ip_alerts = query_manager.get_alerts_by_ip(source_ip="*************")
for alert in ip_alerts['items']:
    detail = detail_manager.get_alert_network_info(alert['id'])
    # 分析请求响应内容
```

### 3. 报告生成
```python
# 生成24小时安全报告
stats = query_manager.get_alert_statistics(hours=24)
print(f"总告警: {stats['total']}")
print(f"高危告警: {stats['high_risk']}")
print(f"Web攻击: {stats['web_attacks']}")
```

### 4. 数据导出
```python
# 导出高危告警数据
query_manager.export_alerts_to_json(
    filename="high_risk_alerts.json",
    hazard_level="高危",
    limit=100
)
```

## 错误处理

### 登录失败
- 自动重试机制（最多5次）
- 验证码识别失败自动重新获取
- 详细的错误日志输出

### 查询失败
- 网络异常自动处理
- 权限不足提示
- 数据格式错误处理

### 常见问题
1. **验证码识别率低**: 可以增加重试次数
2. **网络连接问题**: 检查服务器地址和网络连接
3. **权限不足**: 确认用户账号有相应的查询权限

## 注意事项

1. **依赖要求**: 需要安装 `requests` 和 `ddddocr` 库
2. **网络环境**: 确保能够访问目标服务器
3. **账号权限**: 需要有告警查询和详情查看权限
4. **重试机制**: 默认最多重试5次，可根据需要调整
5. **数据量**: 大量数据查询时建议分页处理

## 许可证

本项目仅供学习和研究使用。
