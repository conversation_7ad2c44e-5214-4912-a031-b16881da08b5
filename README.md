# 奇安信流量传感器API工具

一个用于奇安信流量传感器的Python API工具，支持自动登录、告警查询和详情获取。

## 🚀 功能特性

- ✅ **自动登录**: 支持验证码自动识别和登录重试机制（最多5次）
- ✅ **告警查询**: 支持多种条件的告警列表查询和过滤
- ✅ **详情获取**: 根据告警ID获取详细的数据包信息
- ✅ **数据导出**: 支持将查询结果导出为JSON格式
- ✅ **字段解析**: 自动解析和格式化危险级别、攻击结果等关键字段
- ✅ **统计分析**: 提供告警统计和分析功能

## 📁 文件结构

```
├── login.py           # 登录模块 - 处理登录逻辑和基础API调用
├── query_list.py      # 查询模块 - 告警列表查询和过滤
├── alert_detail.py    # 详情模块 - 根据ID查询告警详情
├── example.py         # 使用示例 - 完整的使用演示
└── README.md          # 说明文档
```

## 📦 依赖安装

```bash
pip install requests ddddocr
```

## 🎯 快速开始

### 基本使用

```python
from query_list import create_query_manager
from alert_detail import AlertDetailManager

# 创建查询管理器（自动处理登录）
query_manager = create_query_manager(
    base_url="https://*************",
    username="admin",
    password="",
    max_retries=5,
    verbose=True
)

if query_manager:
    # 查询告警列表
    alerts = query_manager.get_alert_list(limit=10)
    
    # 查询高危告警
    high_risk = query_manager.get_high_risk_alerts(limit=5)
    
    # 查询告警详情
    detail_manager = AlertDetailManager(query_manager.simulator)
    detail = detail_manager.get_alert_packet_info(alert_id="123456")
```

### 运行示例

```bash
python example.py
```

### 完整使用示例

```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from query_list import create_query_manager
from alert_detail import AlertDetailManager

def main():
    # 1. 创建查询管理器（自动登录）
    print("正在登录...")
    query_manager = create_query_manager(
        base_url="https://*************",
        username="admin",
        password="",
        max_retries=5,
        verbose=True
    )

    if not query_manager:
        print("❌ 登录失败")
        return

    print("✅ 登录成功!")

    # 2. 查询最近的告警
    print("\n📋 查询最近24小时的告警...")
    recent_alerts = query_manager.get_alert_list(limit=5)

    if recent_alerts and recent_alerts.get('items'):
        print(f"找到 {len(recent_alerts['items'])} 条告警")
        for i, alert in enumerate(recent_alerts['items'], 1):
            print(f"  {i}. {alert.get('threat_name', 'Unknown')} - {alert.get('hazard_level', 'Unknown')}")

    # 3. 查询高危告警
    print("\n🔴 查询高危告警...")
    high_risk = query_manager.get_high_risk_alerts(limit=3)

    if high_risk and high_risk.get('items'):
        print(f"找到 {len(high_risk['items'])} 条高危告警")

        # 4. 查询第一个高危告警的详情
        first_alert = high_risk['items'][0]
        alert_id = first_alert.get('id')

        if alert_id:
            print(f"\n🔍 查询告警详情 (ID: {alert_id})...")
            detail_manager = AlertDetailManager(query_manager.simulator)

            # 获取基本信息
            basic_info = detail_manager.get_alert_basic_info(alert_id)
            if basic_info:
                print(f"  规则名称: {basic_info['rule_name']}")
                print(f"  源地址: {basic_info['source_ip']}:{basic_info['source_port']}")
                print(f"  目标地址: {basic_info['dest_ip']}:{basic_info['dest_port']}")

            # 获取网络信息
            network_info = detail_manager.get_alert_network_info(alert_id)
            if network_info:
                print(f"  请求头长度: {len(network_info.get('request_header', ''))}")
                print(f"  响应体长度: {len(network_info.get('response_body', ''))}")

    # 5. 统计分析
    print("\n📊 告警统计...")
    stats = query_manager.get_alert_statistics(hours=24)
    print(f"  总告警数: {stats['total']}")
    print(f"  高危告警: {stats['high_risk']}")
    print(f"  Web攻击: {stats['web_attacks']}")
    print(f"  未处置: {stats['unprocessed']}")

    # 6. 导出数据
    print("\n💾 导出数据...")
    filename = query_manager.export_alerts_to_json(
        filename="recent_alerts.json",
        limit=50
    )
    if filename:
        print(f"  数据已导出到: {filename}")

if __name__ == "__main__":
    main()
```

## 📚 核心模块说明

### 1. login.py - 登录模块

**主要类**: `QianXinLoginSimulator`

**核心功能**:
- 模拟完整的登录流程（13个步骤）
- 自动验证码识别（使用ddddocr）
- 登录重试机制（默认最多5次，间隔2秒）
- 维护登录会话和CSRF令牌
- 提供统一的API请求接口

**主要方法**:
```python
# 创建登录模拟器
simulator = QianXinLoginSimulator("https://*************")

# 登录（带重试机制）
success = simulator.login(
    username="admin", 
    password="", 
    auto_recognize=True, 
    verbose=True, 
    max_retries=5
)

# 检查登录状态
is_logged_in = simulator.is_logged_in()

# 发送API请求
response = simulator.api_request("GET", "/skyeye/alarm/alert_list", params={...})
```

### 2. query_list.py - 查询模块

**主要类**: `AlertQueryManager`

**核心功能**:
- 告警列表查询
- 多种过滤条件支持（时间范围、危险级别、攻击类型等）
- 预定义查询方法（高危告警、Web攻击、弱密码攻击等）
- 统计分析功能
- 数据导出功能

**主要方法**:
```python
# 基本查询
alerts = query_manager.get_alert_list(limit=10)

# 高危告警
high_risk = query_manager.get_high_risk_alerts(limit=5)

# Web攻击
web_attacks = query_manager.get_web_attacks(limit=5)

# 时间范围查询
recent = query_manager.get_alerts_by_time_range(hours_ago=1)

# 弱密码攻击
weak_password = query_manager.get_weak_password_attacks(limit=5)

# 未处置告警
unprocessed = query_manager.get_unprocessed_alerts(limit=5)

# 根据IP地址查询
ip_alerts = query_manager.get_alerts_by_ip(source_ip="*************", limit=5)

# 根据威胁类型查询
xss_alerts = query_manager.get_alerts_by_threat_type("跨站脚本攻击（XSS）", limit=5)

# 多条件搜索
search_results = query_manager.search_alerts(
    hazard_level="高危",
    host_state="成功",
    is_web_attack="1",
    limit=10
)

# 统计分析
stats = query_manager.get_alert_statistics(hours=24)

# 导出数据
filename = query_manager.export_alerts_to_json(filename="alerts.json", limit=100)
```

**支持的过滤条件**:
- `hazard_level`: 危险级别（危急、高危、中危、低危）
- `host_state`: 攻击结果（成功、企图、失败）
- `status`: 处置状态（未处置、已处置等）
- `is_web_attack`: 是否Web攻击（"1"表示是）
- `is_weak_password`: 是否弱密码攻击
- `threat_type`: 威胁类型（如"跨站脚本攻击（XSS）"）
- `threat_name`: 威胁名称
- `sip`/`dip`: 源IP/目标IP地址
- `sport`/`dport`: 源端口/目标端口
- `proto`: 协议类型
- `uri`: 请求URI
- `host`: 主机名
- `attack_stage`: 攻击阶段
- `alarm_source`: 告警源
- `start_time`/`end_time`: 时间范围（毫秒时间戳）

### 3. alert_detail.py - 详情模块

**主要类**: `AlertDetailManager`

**核心功能**:
- 根据告警ID获取详细信息
- 解析数据包信息
- 提取HTTP请求/响应内容
- 格式化显示告警详情
- 导出详情数据

**主要方法**:
```python
# 获取告警数据包信息
packet_info = detail_manager.get_alert_packet_info(alert_id="123456")

# 获取基本信息
basic_info = detail_manager.get_alert_basic_info(alert_id="123456")

# 获取HTTP请求信息
request_info = detail_manager.get_http_request_info(alert_id="123456")

# 获取网络信息（请求/响应）
network_info = detail_manager.get_alert_network_info(alert_id="123456")

# 获取地理位置信息
location_info = detail_manager.get_alert_location_info(alert_id="123456")

# 格式化显示
formatted = detail_manager.format_alert_detail(alert_id="123456", show_full_content=True)

# 导出详情
filename = detail_manager.export_alert_detail_to_json(alert_id="123456")
```

### 4. example.py - 使用示例

包含三个完整的使用示例：

1. **基本示例** (`basic_example()`): 登录 → 查询列表 → 查询详情
2. **高级示例** (`advanced_example()`): 不同类型的查询和统计
3. **导出示例** (`export_example()`): 数据导出功能演示

## 🔧 便捷函数

### 创建查询管理器
```python
from query_list import create_query_manager

query_manager = create_query_manager(
    base_url="https://*************",
    username="admin",
    password="",
    max_retries=5,
    verbose=True
)
```

### 创建详情管理器
```python
from alert_detail import create_alert_detail_manager

detail_manager = create_alert_detail_manager(
    base_url="https://*************",
    username="admin",
    password="",
    max_retries=5,
    verbose=True
)
```

## 📊 数据字段说明

### 告警列表字段
- `id`: 告警ID
- `rule_name`: 规则名称
- `threat_name`: 威胁名称
- `hazard_level`: 危险级别（危急/高危/中危/低危）
- `host_state`: 攻击结果（成功/企图/失败）
- `source_ip`/`source_port`: 源地址和端口
- `dest_ip`/`dest_port`: 目标地址和端口
- `protocol`: 协议类型
- `access_time`: 发生时间

### 告警详情字段
- `request_header`: HTTP请求头
- `request_body`: HTTP请求体
- `response_header`: HTTP响应头
- `response_body`: HTTP响应体
- `packet_info`: 数据包信息
- `rule_info`: 规则信息

## ⚙️ 配置说明

### 登录配置
- `base_url`: 奇安信流量传感器地址
- `username`: 登录用户名
- `password`: 登录密码
- `auto_recognize`: 是否自动识别验证码（默认True）
- `max_retries`: 最大重试次数（默认5次）
- `verbose`: 是否显示详细日志（默认True）

### 查询配置
- `limit`: 每页返回数量（默认10）
- `offset`: 偏移量（默认1）
- `start_time`/`end_time`: 查询时间范围（毫秒时间戳）
- 各种过滤条件

## 🚨 注意事项

1. **验证码识别**: 使用ddddocr库自动识别验证码，识别失败会自动重试
2. **登录重试**: 默认最多重试5次，每次重试间隔2秒
3. **会话保持**: 登录成功后会自动维护会话状态
4. **错误处理**: 完善的错误处理和日志输出
5. **数据格式**: 所有时间戳使用毫秒格式
6. **编码格式**: 导出的JSON文件使用UTF-8编码

## 🔍 故障排除

### 常见问题

1. **登录失败**
   - 检查网络连接
   - 确认用户名密码正确
   - 检查验证码识别是否正常

2. **查询无数据**
   - 检查时间范围设置
   - 确认过滤条件是否正确
   - 验证用户权限

3. **导出失败**
   - 检查文件写入权限
   - 确认磁盘空间充足
   - 验证数据格式

### 调试模式
```python
# 开启详细日志
query_manager = create_query_manager(
    base_url="https://*************",
    username="admin",
    password="",
    verbose=True  # 开启详细日志
)
```
