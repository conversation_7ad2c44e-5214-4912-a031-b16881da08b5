# 登录函数使用说明

## 新的登录函数结构

重新设计了登录函数的结构，将重试逻辑从类内部分离出来，提供更灵活的使用方式。

## 函数说明

### 1. `login_once(username, password, auto_recognize=True, verbose=False)`

**类方法** - 执行一次完整的登录流程（不包含重试逻辑）

- **返回值**: `(success: bool, error_msg: str)` - 登录是否成功和详细错误信息
- **特点**: 提供详细的错误信息，便于调试和自定义重试逻辑

```python
simulator = QianXinLoginSimulator("https://10.228.16.244")
success, error_msg = simulator.login_once("admin", "admin123", verbose=True)
if success:
    print(f"登录成功: {error_msg}")
else:
    print(f"登录失败: {error_msg}")
```

### 2. `login(username, password, auto_recognize=True, verbose=False)`

**类方法** - 简单的登录函数（单次尝试，不包含重试逻辑）

- **返回值**: `bool` - 登录是否成功
- **特点**: 简单易用，适合不需要重试的场景

```python
simulator = QianXinLoginSimulator("https://10.228.16.244")
success = simulator.login("admin", "admin123", verbose=True)
if success:
    print("登录成功")
else:
    print("登录失败")
```

### 3. `login_with_retry(simulator, username, password, auto_recognize=True, verbose=False, max_retries=5)`

**独立函数** - 带重试机制的登录函数

- **返回值**: `bool` - 登录是否成功
- **特点**: 完整的重试逻辑，每次重试都执行完整的登录流程

```python
from login import QianXinLoginSimulator, login_with_retry

simulator = QianXinLoginSimulator("https://10.228.16.244")
success = login_with_retry(
    simulator=simulator,
    username="admin",
    password="admin123",
    auto_recognize=True,
    verbose=True,
    max_retries=5
)
```

## 使用场景

### 场景1: 简单登录（推荐用于生产环境）

```python
from login import QianXinLoginSimulator, login_with_retry

# 创建模拟器
simulator = QianXinLoginSimulator("https://10.228.16.244")

# 使用带重试的登录
success = login_with_retry(simulator, "admin", "admin123", verbose=True)

if success:
    # 登录成功，可以进行API调用
    response = simulator.api_request("GET", "/skyeye/admin/users")
    print(response.json())
```

### 场景2: 自定义重试逻辑

```python
from login import QianXinLoginSimulator
import time

simulator = QianXinLoginSimulator("https://10.228.16.244")

for attempt in range(3):
    success, error_msg = simulator.login_once("admin", "admin123", verbose=True)
    
    if success:
        print("登录成功!")
        break
    else:
        print(f"登录失败: {error_msg}")
        
        # 根据错误类型自定义重试策略
        if "验证码" in error_msg:
            time.sleep(1)  # 验证码错误，短暂等待
        elif "网络" in error_msg:
            time.sleep(5)  # 网络问题，等待更长时间
        else:
            break  # 其他错误，不重试
```

### 场景3: 调试和测试

```python
from login import QianXinLoginSimulator

simulator = QianXinLoginSimulator("https://10.228.16.244")

# 获取详细的错误信息用于调试
success, error_msg = simulator.login_once("admin", "admin123", verbose=True)
print(f"结果: {success}, 详情: {error_msg}")
```

## 重试机制说明

### 完整重试流程

每次重试都会执行以下完整步骤：

1. 重新获取登录页面和CSRF token
2. 重新获取项目配置
3. 重新获取所有必要的资源
4. 重新获取和识别验证码
5. 重新执行登录

### 重试触发条件

- 验证码识别失败
- 登录请求失败
- 任何步骤发生异常
- 关键步骤（如获取登录页面）失败

### 重试间隔

- 默认重试间隔：2秒
- 可以通过自定义重试逻辑调整间隔时间

## 错误信息类型

- `"获取登录页面失败"` - 网络连接问题或服务器不可达
- `"关键步骤异常: xxx"` - 登录页面获取过程中的异常
- `"获取验证码失败"` - 验证码图片获取失败
- `"验证码识别失败"` - 自动识别验证码失败
- `"登录失败，可能是验证码错误或用户名密码错误"` - 登录请求被拒绝
- `"登录过程发生异常: xxx"` - 登录过程中的其他异常

## 注意事项

1. **重试逻辑分离**: 重试逻辑现在完全独立，可以根据需要自定义
2. **错误信息详细**: `login_once` 提供详细的错误信息，便于调试
3. **灵活使用**: 可以选择单次登录、带重试登录或自定义重试逻辑
4. **向后兼容**: 保留了原有的 `login` 方法，但不包含重试逻辑
5. **推荐使用**: 生产环境推荐使用 `login_with_retry` 函数
